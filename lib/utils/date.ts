/**
 * Utility functions for date and time handling
 */

/**
 * Format a date to a time string in the local timezone
 * @param date The date to format
 * @returns Formatted time string in HH:MM format
 */
export function formatTime(date: Date): string {
  try {
    // Use Intl.DateTimeFormat for proper timezone handling
    return new Intl.DateTimeFormat('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
      timeZone: 'Asia/Makassar' // WITA timezone (UTC+8)
    }).format(date);
  } catch (error) {
    console.error("Error formatting time:", error);
    return "00:00"; // Fallback
  }
}

/**
 * Format a date to a date string in the local timezone
 * @param date The date to format
 * @returns Formatted date string
 */
export function formatDate(date: Date): string {
  try {
    // Use Intl.DateTimeFormat for proper timezone handling
    return new Intl.DateTimeFormat('id-ID', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      timeZone: 'Asia/Makassar' // WITA timezone (UTC+8)
    }).format(date);
  } catch (error) {
    console.error("Error formatting date:", error);
    return ""; // Fallback
  }
}

/**
 * Get the current date and time in WITA timezone
 * @returns Object with formatted date and time strings
 */
export function getCurrentDateTime(): { date: string, time: string } {
  const now = new Date();
  return {
    date: formatDate(now),
    time: formatTime(now)
  };
}

/**
 * Create a Date object for the start of the day in WITA timezone
 * @param date Optional date to use (defaults to today)
 * @returns Date object set to the start of the day
 */
export function getStartOfDay(date: Date = new Date()): Date {
  // Create a new date to avoid modifying the original
  const newDate = new Date(date);
  newDate.setHours(0, 0, 0, 0);
  return newDate;
}

/**
 * Create a Date object for the end of the day in WITA timezone
 * @param date Optional date to use (defaults to today)
 * @returns Date object set to the end of the day
 */
export function getEndOfDay(date: Date = new Date()): Date {
  // Create a new date to avoid modifying the original
  const newDate = new Date(date);
  newDate.setHours(23, 59, 59, 999);
  return newDate;
}
