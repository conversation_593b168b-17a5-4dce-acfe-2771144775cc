/**
 * Application configuration
 *
 * This file centralizes all configuration values from environment variables.
 * It ensures that required environment variables are present and provides
 * type-safe access to them throughout the application.
 */

// Environment
export const NODE_ENV = process.env.NODE_ENV || 'development'
export const IS_DEVELOPMENT = NODE_ENV === 'development'
export const IS_PRODUCTION = NODE_ENV === 'production'
export const IS_TEST = NODE_ENV === 'test'
export const DOMAIN = process.env.DOMAIN || 'localhost'
export const STUDENT_DOMAIN = process.env.STUDENT_DOMAIN || 'shalatyuk.libstudio.my.id'
export const ADMIN_DOMAIN = process.env.ADMIN_DOMAIN || 'adminshalat.libstudio.my.id'

// N8N Webhook
export const N8N_WHATSAPP_WEBHOOK_URL = process.env.N8N_WHATSAPP_WEBHOOK_URL

// Database
export const DATABASE_URL =
  process.env.DATABASE_URL || 'postgres://postgres:postgres@localhost:5432/shalat_yuk'
if (!DATABASE_URL && IS_PRODUCTION) {
  console.error('WARNING: DATABASE_URL environment variable is missing in production')
}

// Redis
export const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379'
if (!REDIS_URL && IS_PRODUCTION) {
  console.error('WARNING: REDIS_URL environment variable is missing in production')
}

// Authentication
export const JWT_SECRET = process.env.JWT_SECRET || 'development-secret-key-replace-in-production'
if (!JWT_SECRET && IS_PRODUCTION) {
  console.error('WARNING: JWT_SECRET environment variable is missing in production')
}

// NextAuth (legacy - keeping URL for compatibility)
export const NEXTAUTH_URL = process.env.NEXTAUTH_URL
export const NEXTAUTH_SECRET = process.env.NEXTAUTH_SECRET || 'development-secret'
// Not throwing error for NEXTAUTH_SECRET since we're not using NextAuth anymore

// Google OAuth
export const GOOGLE_CLIENT_ID = process.env.GOOGLE_CLIENT_ID
export const GOOGLE_CLIENT_SECRET = process.env.GOOGLE_CLIENT_SECRET
if ((!GOOGLE_CLIENT_ID || !GOOGLE_CLIENT_SECRET) && IS_PRODUCTION) {
  console.error(
    'WARNING: GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables are missing in production'
  )
}

// Server-side configuration object
export const serverConfig = {
  database: {
    url: DATABASE_URL,
  },
  redis: {
    url: REDIS_URL,
  },
  auth: {
    jwtSecret: JWT_SECRET,
    nextAuthUrl: NEXTAUTH_URL,
    nextAuthSecret: NEXTAUTH_SECRET,
    googleClientId: GOOGLE_CLIENT_ID,
    googleClientSecret: GOOGLE_CLIENT_SECRET,
  },
  environment: {
    nodeEnv: NODE_ENV,
    isDevelopment: IS_DEVELOPMENT,
    isProduction: IS_PRODUCTION,
    isTest: IS_TEST,
    domain: DOMAIN,
    studentDomain: STUDENT_DOMAIN,
    adminDomain: ADMIN_DOMAIN,
  },
  webhooks: {
    n8nWhatsapp: N8N_WHATSAPP_WEBHOOK_URL,
  },
}

// Client-side safe configuration object (no secrets)
export const clientConfig = {
  environment: {
    nodeEnv: NODE_ENV,
    isDevelopment: IS_DEVELOPMENT,
    isProduction: IS_PRODUCTION,
    isTest: IS_TEST,
    domain: DOMAIN,
    studentDomain: STUDENT_DOMAIN,
    adminDomain: ADMIN_DOMAIN,
  },
  auth: {
    nextAuthUrl: NEXTAUTH_URL,
    googleClientId: GOOGLE_CLIENT_ID,
  },
}
