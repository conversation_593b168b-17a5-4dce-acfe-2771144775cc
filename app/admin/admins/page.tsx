'use client'

import type React from 'react'
import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { ThemeToggle } from '@/components/theme-toggle'
import { AdminBottomNav } from '@/components/admin-bottom-nav'
import { useToast } from '@/components/ui/use-toast'
import {
  Edit,
  Plus,
  Trash2,
  Loader2,
  AlertCircle,
  Shield,
  ShieldCheck,
  Search,
  X,
} from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAdminSession } from '@/hooks/use-admin-session'
import { useAdminPermission } from '@/hooks/use-admin-permission'
import { useRouter } from 'next/navigation'

// Admin type definition
interface Admin {
  id: number
  name: string
  username: string
  role: 'admin' | 'super_admin'
}

// Form data type
interface FormData {
  id: string
  name: string
  username: string
  role: string
  password: string
}

// Interface for form validation errors
interface FormErrors {
  password?: string
  username?: string
  name?: string
}

// Define the dialog mode type explicitly
type DialogMode = 'add' | 'edit'

export default function AdminManagement() {
  const { toast } = useToast()
  const { admin } = useAdminSession()
  const { permission, loading: permissionLoading } = useAdminPermission('super_admin')
  const router = useRouter()
  const [showDialog, setShowDialog] = useState(false)
  const [dialogMode, setDialogMode] = useState<DialogMode>('add')
  const [selectedAdmin, setSelectedAdmin] = useState<null | Admin>(null)
  const [formData, setFormData] = useState<FormData>({
    id: '',
    name: '',
    username: '',
    role: 'admin',
    password: '',
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  // showPasswordAlert removed as it's not used
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [admins, setAdmins] = useState<Admin[]>([])
  const [filteredAdmins, setFilteredAdmins] = useState<Admin[]>([])
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deleteError, setDeleteError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')

  // Fetch admins from API
  const fetchAdmins = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/admins')

      if (!response.ok) {
        throw new Error('Failed to fetch admins')
      }

      const data = await response.json()
      // Data is already filtered to admin and super_admin users
      setAdmins(data)
      return data
    } catch (error) {
      console.error('Error fetching admins:', error)
      toast({
        title: 'Error',
        description: 'Gagal mengambil data admin',
        variant: 'destructive',
      })
      return []
    } finally {
      setIsLoading(false)
    }
  }

  // Load data on component mount
  useEffect(() => {
    fetchAdmins()
  }, [])

  // Filter admins whenever search query, role filter, or admins array changes
  useEffect(() => {
    let result = [...admins]

    // Filter by role if not 'all'
    if (roleFilter !== 'all') {
      result = result.filter(admin => admin.role === roleFilter)
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(
        admin =>
          admin.name.toLowerCase().includes(query) || admin.username.toLowerCase().includes(query)
      )
    }

    setFilteredAdmins(result)
  }, [admins, searchQuery, roleFilter])

  const handleAddAdmin = () => {
    setDialogMode('add')
    setFormData({
      id: '',
      name: '',
      username: '',
      role: 'admin',
      password: '',
    })
    setFormErrors({})
    setShowDialog(true)
  }

  const handleEditAdmin = (admin: Admin) => {
    setDialogMode('edit')
    setSelectedAdmin(admin)
    setFormData({
      id: admin.id.toString(),
      name: admin.name,
      username: admin.username,
      role: admin.role,
      password: '',
    })
    setFormErrors({})
    setShowDialog(true)
  }

  const handleDeleteClick = (admin: Admin) => {
    setSelectedAdmin(admin)
    setDeleteError(null)
    setShowDeleteDialog(true)
  }

  const handleDeleteConfirm = async () => {
    if (!selectedAdmin) return

    try {
      setIsSubmitting(true)
      setDeleteError(null)

      const response = await fetch(`/api/admins/${selectedAdmin.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || errorData.message || 'Failed to delete admin')
      }

      setShowDeleteDialog(false)
      fetchAdmins()

      toast({
        title: 'Admin dihapus',
        description: `${selectedAdmin.name} telah dihapus dari sistem`,
      })
    } catch (error) {
      console.error('Error deleting admin:', error)
      const errorMessage = error instanceof Error ? error.message : 'Gagal menghapus admin'
      setDeleteError(errorMessage)
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Clear any previous errors
    setFormErrors({})

    // Validation
    const errors: FormErrors = {}

    if (!formData.name.trim()) {
      errors.name = 'Nama wajib diisi'
    }

    if (!formData.username.trim()) {
      errors.username = 'Username wajib diisi'
    } else if (formData.username.length < 3) {
      errors.username = 'Username minimal 3 karakter'
    }

    // Password validation
    if (dialogMode === 'add' && formData.password.length < 6) {
      errors.password = 'Password minimal 6 karakter'
    }

    if (dialogMode === 'edit' && formData.password && formData.password.length < 6) {
      errors.password = 'Password minimal 6 karakter'
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }

    setIsSubmitting(true)

    try {
      let endpoint = '/api/users'
      let method = 'POST'
      let requestData: Record<string, any> = {}

      if (dialogMode === 'add') {
        requestData = {
          role: formData.role,
          name: formData.name,
          username: formData.username,
          password: formData.password,
        }
      } else {
        if (!selectedAdmin) throw new Error('No admin selected for edit')
        endpoint = `/api/users/${selectedAdmin.id}`
        method = 'PATCH'
        requestData = {
          role: formData.role,
          name: formData.name,
          ...(formData.password ? { password: formData.password } : {}),
        }
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || errorData.message || 'Failed to save admin')
      }

      setShowDialog(false)
      fetchAdmins()

      toast({
        title: dialogMode === 'add' ? 'Admin ditambahkan' : 'Admin diperbarui',
        description:
          dialogMode === 'add'
            ? `${formData.name} telah ditambahkan sebagai ${formData.role}`
            : `Data ${formData.name} telah diperbarui`,
      })
    } catch (error) {
      console.error(`Error ${dialogMode === 'add' ? 'adding' : 'updating'} admin:`, error)
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : `Gagal ${dialogMode === 'add' ? 'menambahkan' : 'memperbarui'} admin`,
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Show loading state while checking permissions
  if (permissionLoading) {
    return (
      <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
        <header className="flex items-center justify-between p-4">
          <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Manajemen Admin</h1>
          <ThemeToggle />
        </header>
        <main className="container mx-auto px-4">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </main>
        <AdminBottomNav activeTab="admins" adminRole={admin?.role} />
      </div>
    )
  }

  // Show access denied if not super admin
  if (!permission) {
    return (
      <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
        <header className="flex items-center justify-between p-4">
          <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Manajemen Admin</h1>
          <ThemeToggle />
        </header>

        <main className="container mx-auto px-4">
          <Card className="p-8 text-center">
            <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" />
            <h2 className="mb-2 text-xl font-bold text-red-500">Akses Ditolak</h2>
            <p className="mb-4 text-slate-600 dark:text-slate-400">
              Anda tidak memiliki izin untuk mengakses halaman ini. Hanya Super Admin yang dapat
              mengakses halaman Manajemen Admin.
            </p>
            <Button
              onClick={() => router.push('/admin/home')}
              className="bg-indigo-600 text-white hover:bg-indigo-700"
            >
              Kembali ke Beranda
            </Button>
          </Card>
        </main>

        <AdminBottomNav activeTab="admins" adminRole={admin?.role} />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
      <header className="flex items-center justify-between p-4">
        <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Manajemen Admin</h1>
        <ThemeToggle />
      </header>

      <main className="container mx-auto px-4">
        {/* Search and Filter Controls */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="flex flex-1 items-center gap-2">
                <Search className="h-4 w-4 text-slate-500" />
                <Input
                  placeholder="Cari admin..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="max-w-sm"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSearchQuery('')}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>

              <div className="flex items-center gap-2">
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Filter Role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Role</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="super_admin">Super Admin</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  onClick={handleAddAdmin}
                  className="bg-indigo-600 text-white hover:bg-indigo-700"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Tambah Admin
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Admin Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Daftar Admin ({filteredAdmins.length})</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : filteredAdmins.length === 0 ? (
              <div className="py-8 text-center text-slate-500">
                {searchQuery || roleFilter !== 'all'
                  ? 'Tidak ada admin yang sesuai dengan filter'
                  : 'Belum ada admin'}
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Nama</TableHead>
                      <TableHead>Username</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead className="text-right">Aksi</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAdmins.map(admin => (
                      <TableRow key={admin.id}>
                        <TableCell className="font-medium">{admin.name}</TableCell>
                        <TableCell>{admin.username}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {admin.role === 'super_admin' ? (
                              <ShieldCheck className="h-4 w-4 text-red-500" />
                            ) : (
                              <Shield className="h-4 w-4 text-blue-500" />
                            )}
                            <span
                              className={
                                admin.role === 'super_admin' ? 'text-red-600' : 'text-blue-600'
                              }
                            >
                              {admin.role === 'super_admin' ? 'Super Admin' : 'Admin'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditAdmin(admin)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteClick(admin)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </main>

      <AdminBottomNav activeTab="admins" adminRole={admin?.role} />

      {/* Add/Edit Admin Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{dialogMode === 'add' ? 'Tambah Admin Baru' : 'Edit Admin'}</DialogTitle>
            <DialogDescription>
              {dialogMode === 'add' ? 'Masukkan informasi admin baru' : 'Perbarui informasi admin'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Nama
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={e => setFormData({ ...formData, name: e.target.value })}
                  className="col-span-3"
                  required
                />
                {formErrors.name && (
                  <div className="col-span-4 text-sm text-red-500">{formErrors.name}</div>
                )}
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="username" className="text-right">
                  Username
                </Label>
                <Input
                  id="username"
                  value={formData.username}
                  onChange={e => setFormData({ ...formData, username: e.target.value })}
                  className="col-span-3"
                  required
                  disabled={dialogMode === 'edit'}
                />
                {formErrors.username && (
                  <div className="col-span-4 text-sm text-red-500">{formErrors.username}</div>
                )}
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="role" className="text-right">
                  Role
                </Label>
                <Select
                  value={formData.role}
                  onValueChange={value => setFormData({ ...formData, role: value })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Pilih role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="super_admin">Super Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="password" className="text-right">
                  Password
                </Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={e => setFormData({ ...formData, password: e.target.value })}
                  className="col-span-3"
                  placeholder={dialogMode === 'edit' ? 'Kosongkan jika tidak ingin mengubah' : ''}
                  required={dialogMode === 'add'}
                />
                {formErrors.password && (
                  <div className="col-span-4 text-sm text-red-500">{formErrors.password}</div>
                )}
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowDialog(false)}>
                Batal
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {dialogMode === 'add' ? 'Menambahkan...' : 'Memperbarui...'}
                  </>
                ) : dialogMode === 'add' ? (
                  'Tambah'
                ) : (
                  'Perbarui'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus Admin</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus admin <strong>{selectedAdmin?.name}</strong>?
              Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          {deleteError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{deleteError}</AlertDescription>
            </Alert>
          )}
          <AlertDialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Batal
            </Button>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isSubmitting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menghapus...
                </>
              ) : (
                'Hapus'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
