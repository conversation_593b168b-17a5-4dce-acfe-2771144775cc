import { NextRequest, NextResponse } from 'next/server'
import { UserUseCases } from '@/lib/domain/usecases/user'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { ClassRepository } from '@/lib/data/repositories/class'
import { AbsenceRepository } from '@/lib/data/repositories/absence'
import { getRedisCache } from '@/lib/data/cache/redis'
import { authenticateAdmin, handleAuthError } from '@/lib/middleware/auth'
import { z } from 'zod'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const classRepo = new ClassRepository()
const absenceRepo = new AbsenceRepository()
const userUseCases = new UserUseCases(studentRepo, adminRepo, classRepo, cache, absenceRepo)

// Schema for updating admin
const updateAdminSchema = z.object({
  role: z.enum(['admin', 'super_admin']).optional(),
  name: z.string().min(1, 'Nama wajib diisi').optional(),
  password: z.string().min(6, 'Password minimal 6 karakter').optional(),
})

/**
 * GET /api/admins/[id]
 * Get a specific admin by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    const adminId = parseInt(params.id)
    if (isNaN(adminId)) {
      return NextResponse.json(
        { message: 'ID admin tidak valid' },
        { status: 400 }
      )
    }

    console.info('Fetching admin by ID:', adminId)
    const admin = await userUseCases.getUserById(adminId)

    if (!admin) {
      return NextResponse.json(
        { message: 'Admin tidak ditemukan' },
        { status: 404 }
      )
    }

    // Check if user is actually an admin
    if (admin.role !== 'admin' && admin.role !== 'super_admin') {
      return NextResponse.json(
        { message: 'User bukan admin' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      id: admin.id,
      name: admin.name,
      username: admin.username,
      role: admin.role,
    })
  } catch (error) {
    console.error('Error fetching admin:', error)
    return NextResponse.json(
      { message: 'Gagal mengambil data admin' },
      { status: 500 }
    )
  }
}

/**
 * PATCH /api/admins/[id]
 * Update an admin user
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    const adminId = parseInt(params.id)
    if (isNaN(adminId)) {
      return NextResponse.json(
        { message: 'ID admin tidak valid' },
        { status: 400 }
      )
    }

    const body = await req.json()
    const validation = updateAdminSchema.safeParse(body)

    if (!validation.success) {
      console.error('Admin update validation failed:', validation.error.format())
      return NextResponse.json(
        { message: 'Validasi gagal', errors: validation.error.format() },
        { status: 400 }
      )
    }

    const validatedData = validation.data

    console.info('Attempting to update admin:', { id: adminId, data: validatedData })

    // Check if admin exists and is actually an admin
    const existingAdmin = await userUseCases.getUserById(adminId)
    if (!existingAdmin) {
      return NextResponse.json(
        { message: 'Admin tidak ditemukan' },
        { status: 404 }
      )
    }

    if (existingAdmin.role !== 'admin' && existingAdmin.role !== 'super_admin') {
      return NextResponse.json(
        { message: 'User bukan admin' },
        { status: 404 }
      )
    }

    const updatedAdmin = await userUseCases.updateUser(adminId, validatedData)

    console.info('Admin updated successfully:', { id: adminId })

    // Clear cache
    await cache.del('users:all')
    await cache.del(`user:${adminId}`)

    return NextResponse.json({
      id: updatedAdmin.id,
      name: updatedAdmin.name,
      username: updatedAdmin.username,
      role: updatedAdmin.role,
    })

  } catch (error) {
    console.error('Error updating admin:', error)
    return NextResponse.json(
      { message: 'Gagal memperbarui admin' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/admins/[id]
 * Delete an admin user
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate the admin
    try {
      await authenticateAdmin(req)
    } catch (authError) {
      return handleAuthError(authError)
    }

    const adminId = parseInt(params.id)
    if (isNaN(adminId)) {
      return NextResponse.json(
        { message: 'ID admin tidak valid' },
        { status: 400 }
      )
    }

    console.info('Attempting to delete admin:', adminId)

    // Check if admin exists and is actually an admin
    const existingAdmin = await userUseCases.getUserById(adminId)
    if (!existingAdmin) {
      return NextResponse.json(
        { message: 'Admin tidak ditemukan' },
        { status: 404 }
      )
    }

    if (existingAdmin.role !== 'admin' && existingAdmin.role !== 'super_admin') {
      return NextResponse.json(
        { message: 'User bukan admin' },
        { status: 404 }
      )
    }

    await userUseCases.deleteUser(adminId)

    console.info('Admin deleted successfully:', { id: adminId })

    // Clear cache
    await cache.del('users:all')
    await cache.del(`user:${adminId}`)

    return NextResponse.json(
      { message: 'Admin berhasil dihapus' },
      { status: 200 }
    )

  } catch (error) {
    console.error('Error deleting admin:', error)
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('cannot delete') || error.message.includes('constraint')) {
        return NextResponse.json(
          { message: 'Admin tidak dapat dihapus karena masih memiliki data terkait' },
          { status: 409 }
        )
      }
    }

    return NextResponse.json(
      { message: 'Gagal menghapus admin' },
      { status: 500 }
    )
  }
}
