# User Management Refactoring & Bulk QR Code Download - TODO

## Overview

Refactoring user management to separate student and admin management, plus implementing bulk QR code download feature.

## High Priority Tasks

### 1. Create Admin Management Page

- [] Create `/app/admin/admins/page.tsx` for admin and super admin users
- [] Implement admin-specific CRUD operations
- [] Add permission checks (super admin only)
- [] Create admin-specific interfaces and types

### 2. Update Navigation

- [] Update `AdminBottomNav` component to include "Admin" menu item
- [] Add appropriate icons and routing
- [] Implement permission-based visibility

### 3. Refactor Existing Users Page

- [] Rename current page to "Manajemen Siswa" (Student Management)
- [] Filter to show only student users
- [] Remove admin/super admin related functionality
- [] Keep bulk operations for students only

## Medium Priority Tasks

### 4. Implement Bulk QR Code Download (HOLD)

- [ ] Install required dependencies (`jszip`, `qrcode`, `file-saver`)
- [ ] Create API endpoint `/api/students/bulk-qr-download`
- [ ] Implement server-side QR generation and ZIP creation
- [ ] Add UI components for download options:
  - [ ] Download all students
  - [ ] Download selected students
  - [ ] Download by class
- [ ] Add progress dialog with real-time updates
- [ ] Implement chunked processing for performance

### 5. Create Shared Components (HOLD)

- [ ] Extract common user management logic into hooks
- [ ] Create reusable components for user forms
- [ ] Implement shared permission checking utilities

### 6. API Endpoints

- [] Create `/api/admins/` endpoints for admin management
- [] Update existing `/api/users/` endpoints to handle separation
- [ ] Implement bulk QR generation endpoint

## Low Priority Tasks

### 7. Performance Optimization (HOLD)

- [ ] Implement database indexing for user queries (HOLD)
- [ ] Add caching for frequently accessed data (HOLD)
- [ ] Optimize QR code generation for large batches (HOLD)

## Technical Implementation Details

### Directory Structure

```
app/admin/
├── users/page.tsx          # Student Management (refactored)
├── admins/page.tsx         # Admin Management (new)
└── ...

components/
├── admin-bottom-nav.tsx    # Updated navigation
├── shared/
│   ├── user-form.tsx      # Shared user form component
│   ├── bulk-operations.tsx # Shared bulk operations
│   └── qr-download.tsx    # QR download components
└── ...

api/
├── users/                  # Student-focused endpoints
├── admins/                 # Admin-focused endpoints
└── students/
    └── bulk-qr-download/   # QR generation endpoint
```

### Key Features by Page

#### Student Management (`/admin/users`)

- View, add, edit, delete students
- Bulk upload students via CSV
- Bulk update student classes
- Bulk delete students
- **Bulk QR code download** (new feature)
- Search and filter by class
- Accessible by super admins

#### Admin Management (`/admin/admins`)

- View, add, edit, delete admin users
- Role management (admin/super admin)
- Permission management
- Search and filter capabilities
- Accessible by super admins only

## Dependencies to Install

```bash
npm install jszip qrcode file-saver
npm install --save-dev @types/qrcode
```

## Progress Tracking

- [✅] Phase 1: Create admin page and update navigation
- [✅] Phase 2: Refactor users page for students only
- [ ] Phase 3: API endpoints for admin management
- [ ] Phase 4: Implement bulk QR code download (HOLD)
- [ ] Phase 5: Extract shared components and optimize

---

**Last Updated:** $(date)
**Status:** Implementation Complete ✅
