'use client'

import { useRouter } from 'next/navigation'
import { Camera, FileText, User, Users, GraduationCap } from 'lucide-react'

interface AdminBottomNavProps {
  activeTab: 'home' | 'reports' | 'users' | 'admins' | 'classes' | 'profile'
  adminRole?: 'admin' | 'super_admin'
}

export function AdminBottomNav({ activeTab, adminRole = 'admin' }: AdminBottomNavProps) {
  const router = useRouter()
  const isSuperAdmin = adminRole === 'super_admin'

  return (
    <div className="fixed bottom-0 left-0 right-0 flex h-16 items-center justify-around border-t border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-800 md:hidden">
      <button
        onClick={() => router.push('/admin/home')}
        className={`flex h-full w-full flex-col items-center justify-center transition-colors ${
          activeTab === 'home'
            ? 'text-indigo-600 dark:text-indigo-400'
            : 'text-slate-500 hover:bg-indigo-50 dark:text-slate-400 dark:hover:bg-slate-700'
        }`}
      >
        <Camera className="h-5 w-5" />
        <span className="mt-1 text-xs">Scanner</span>
      </button>

      {/* <button
        onClick={() => router.push("/admin/dashboard")}
        className={`flex flex-col items-center justify-center w-full h-full transition-colors ${
          activeTab === "dashboard"
            ? "text-indigo-600 dark:text-indigo-400"
            : "text-slate-500 dark:text-slate-400 hover:bg-indigo-50 dark:hover:bg-slate-700"
        }`}
      >
        <BarChart className="h-5 w-5" />
        <span className="text-xs mt-1">Dashboard</span>
      </button> */}

      <button
        onClick={() => router.push('/admin/reports')}
        className={`flex h-full w-full flex-col items-center justify-center transition-colors ${
          activeTab === 'reports'
            ? 'text-indigo-600 dark:text-indigo-400'
            : 'text-slate-500 hover:bg-indigo-50 dark:text-slate-400 dark:hover:bg-slate-700'
        }`}
      >
        <FileText className="h-5 w-5" />
        <span className="mt-1 text-xs">Laporan</span>
      </button>

      {isSuperAdmin && (
        <button
          onClick={() => router.push('/admin/users')}
          className={`flex h-full w-full flex-col items-center justify-center transition-colors ${
            activeTab === 'users'
              ? 'text-indigo-600 dark:text-indigo-400'
              : 'text-slate-500 hover:bg-indigo-50 dark:text-slate-400 dark:hover:bg-slate-700'
          }`}
        >
          <Users className="h-5 w-5" />
          <span className="mt-1 text-xs">Siswa</span>
        </button>
      )}

      {isSuperAdmin && (
        <button
          onClick={() => router.push('/admin/admins')}
          className={`flex h-full w-full flex-col items-center justify-center transition-colors ${
            activeTab === 'admins'
              ? 'text-indigo-600 dark:text-indigo-400'
              : 'text-slate-500 hover:bg-indigo-50 dark:text-slate-400 dark:hover:bg-slate-700'
          }`}
        >
          <Users className="h-5 w-5" />
          <span className="mt-1 text-xs">Admin</span>
        </button>
      )}

      {isSuperAdmin && (
        <button
          onClick={() => router.push('/admin/classes')}
          className={`flex h-full w-full flex-col items-center justify-center transition-colors ${
            activeTab === 'classes'
              ? 'text-indigo-600 dark:text-indigo-400'
              : 'text-slate-500 hover:bg-indigo-50 dark:text-slate-400 dark:hover:bg-slate-700'
          }`}
        >
          <GraduationCap className="h-5 w-5" />
          <span className="mt-1 text-xs">Kelas</span>
        </button>
      )}

      <button
        onClick={() => router.push('/admin/profile')}
        className={`flex h-full w-full flex-col items-center justify-center transition-colors ${
          activeTab === 'profile'
            ? 'text-indigo-600 dark:text-indigo-400'
            : 'text-slate-500 hover:bg-indigo-50 dark:text-slate-400 dark:hover:bg-slate-700'
        }`}
      >
        <User className="h-5 w-5" />
        <span className="mt-1 text-xs">Profil</span>
      </button>
    </div>
  )
}
